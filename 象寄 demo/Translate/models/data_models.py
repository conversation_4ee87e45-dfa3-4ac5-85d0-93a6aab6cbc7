"""
数据模型定义
定义模块间传递的标准化数据结构
"""
from dataclasses import dataclass
from typing import List, Tuple, Dict, Any, Optional
import numpy as np


@dataclass
class TextRegion:
    """文字区域基础数据结构"""
    id: int
    text: str
    bbox: Tuple[int, int, int, int]  # (x, y, w, h)
    poly: np.ndarray
    score: float
    is_chinese: bool
    center: Tuple[int, int]
    style_info: Optional[Dict[str, Any]] = None  # 新增：样式信息
    
    @classmethod
    def from_ocr_result(cls, region_id: int, poly: np.ndarray, text: str, score: float, is_chinese: bool):
        """从OCR结果创建TextRegion"""
        import cv2
        points = np.array(poly, dtype=np.int32)
        x, y, w, h = cv2.boundingRect(points)
        center = (x + w//2, y + h//2)
        
        return cls(
            id=region_id,
            text=text,
            bbox=(x, y, w, h),
            poly=poly,
            score=score,
            is_chinese=is_chinese,
            center=center
        )


@dataclass
class OCRResult:
    """OCR处理结果"""
    dt_polys: List[np.ndarray]
    rec_texts: List[str]
    rec_scores: List[float]
    chinese_regions: List[TextRegion]
    other_regions: List[TextRegion]
    
    @property
    def total_regions(self) -> int:
        return len(self.chinese_regions) + len(self.other_regions)
    
    @property
    def chinese_count(self) -> int:
        return len(self.chinese_regions)


@dataclass
class LayoutResult:
    """布局分析结果"""
    layout_mode: str
    regions: List[Dict[str, Any]]
    horizontal_alignment: Dict[str, Any]
    vertical_distribution: Dict[str, Any]
    alignment_strategies: List[Dict[str, Any]]


@dataclass
class FontMatchResult:
    """字体匹配结果"""
    text: str
    matched_font: str
    font_path: str
    confidence: float
    supports_japanese: bool
    region_id: int


@dataclass
class StyleInfo:
    """文字样式信息"""
    estimated_font_size: int
    precise_height: int  # 新增：精确高度测量
    color: Tuple[int, int, int]
    background_color: Tuple[int, int, int]
    is_bold: bool
    is_dark_text: bool  # 新增：是否为深色文字
    contrast_ratio: float


@dataclass
class TranslationResult:
    """翻译处理结果"""
    original_text: str
    translated_text: str
    bbox: Tuple[int, int, int, int]
    style_info: StyleInfo
    font_info: FontMatchResult
    group_key: str
    group_scale_factor: float

    # 初始计算结果（用于分组约束）
    initial_font_size: Optional[int] = None
    initial_text_width: Optional[int] = None
    initial_text_height: Optional[int] = None

    # 最终字体大小（经过像素级匹配+统一缩放）
    final_font_size: Optional[int] = None

    # Gemini分析的字号（用于对比）
    gemini_font_size: Optional[int] = None

    # 程序计算的字号（用于对比）
    program_font_size: Optional[int] = None

    # 渲染相关属性
    text_x: Optional[int] = None
    text_y: Optional[int] = None
    text_width: Optional[int] = None
    text_height: Optional[int] = None
    font_size: Optional[int] = None


@dataclass
class RenderConfig:
    """渲染配置"""
    text_x: int
    text_y: int
    text_width: int
    text_height: int
    font_size: int
    font_path: str
    alignment_type: str  # 'left', 'center', 'right'
    color: Tuple[int, int, int]


@dataclass
class ProcessingResult:
    """处理结果包装器"""
    success: bool
    data: Any = None
    error_message: str = ""
    
    @classmethod
    def success_result(cls, data: Any):
        return cls(success=True, data=data)
    
    @classmethod
    def error_result(cls, error_message: str):
        return cls(success=False, error_message=error_message)


@dataclass
class PipelineConfig:
    """流水线配置"""
    # OCR配置
    ocr_confidence_threshold: float = 0.5
    
    # 布局分析配置
    alignment_threshold: int = 5
    proximity_threshold: int = 50
    
    # 字体配置
    default_font_weight: int = 400
    font_size_range: Tuple[int, int] = (12, 72)
    
    # 路径配置
    fonts_dir: str = "fonts"
    output_dir: str = "output"
    
    # 大模型翻译配置
    llm_provider: str = "openai"  # 当前使用的大模型提供商
    llm_timeout: int = 30
    llm_max_retries: int = 3
    
    # OpenAI配置
    openai_api_key: str = "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"
    openai_base_url: str = "https://openrouter.ai/api/v1"
    openai_model: str = "openai/gpt-4o-mini"
    openai_temperature: float = 0.1
    openai_max_tokens: int = 1000
    
    # 图像修复配置
    inpaint_engine: str = "lama"  # 默认使用LaMa引擎
    inpaint_config: Dict[str, Any] = None

    # 调试配置
    enable_debug_output: bool = True
    debug_output_dir: str = "output"
    
    # OCR调试配置
    enable_ocr_debug: bool = False
    ocr_debug_dir: str = "debug_images/ocr_processor"
    
    # Layout调试配置
    enable_layout_debug: bool = False
    layout_debug_dir: str = "debug_images/layout_processor"
    
    # Translation调试配置
    enable_translation_debug: bool = False
    translation_debug_dir: str = "debug_images/translation_processor"
    
    # Inpaint调试配置
    enable_inpaint_debug: bool = False
    inpaint_debug_dir: str = "debug_images/inpaint_processor"
    
    def __post_init__(self):
        """初始化后处理"""
        if self.inpaint_config is None:
            # M1芯片默认配置
            self.inpaint_config = {
                'device': 'mps',  # M1芯片使用MPS
                'quality': 'high',
                'model_path': 'models/lama',
                'input_size': 512
            }


# 类型别名
BBox = Tuple[int, int, int, int]  # (x, y, w, h)
Point = Tuple[int, int]  # (x, y)
Color = Tuple[int, int, int]  # (r, g, b)
